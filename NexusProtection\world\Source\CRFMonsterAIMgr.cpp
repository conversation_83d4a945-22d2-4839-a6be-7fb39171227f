/**
 * @file CRFMonsterAIMgr.cpp
 * @brief Implementation of RF Monster AI Manager for managing monster artificial intelligence
 * @details Provides singleton management for monster AI state tables and processing
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "CRFMonsterAIMgr.h"
#include <algorithm>
#include <chrono>
#include <stdexcept>

#include <chrono>

// Forward declarations for legacy classes
template<typename T>
class UsPoint {
public:
    UsPoint() : m_data(nullptr) {}
    explicit UsPoint(T* data) : m_data(data) {}
    ~UsPoint() = default;
    
    // Copy constructor and assignment
    UsPoint(const UsPoint& other) : m_data(other.m_data) {}
    UsPoint& operator=(const UsPoint& other) {
        if (this != &other) {
            m_data = other.m_data;
        }
        return *this;
    }
    
    // Move constructor and assignment
    UsPoint(UsPoint&& other) noexcept : m_data(other.m_data) {
        other.m_data = nullptr;
    }
    UsPoint& operator=(UsPoint&& other) noexcept {
        if (this != &other) {
            m_data = other.m_data;
            other.m_data = nullptr;
        }
        return *this;
    }
    
    T* Get() const { return m_data; }
    bool IsValid() const { return m_data != nullptr; }
    
private:
    T* m_data;
};

class UsStateTBL {
public:
    UsStateTBL() = default;
    ~UsStateTBL() = default;
    // Add minimal interface as needed
};

namespace NexusProtection {
namespace World {

// Static member definitions
CRFMonsterAIMgr* CRFMonsterAIMgr::ms_Instance = nullptr;
std::mutex CRFMonsterAIMgr::ms_InstanceMutex;

// Constructor
CRFMonsterAIMgr::CRFMonsterAIMgr()
    : m_isInitialized(false)
    , m_statistics{} {
    InitializeInternal();
}

// Destructor
CRFMonsterAIMgr::~CRFMonsterAIMgr() {
    Shutdown();
}

// Get the singleton instance
CRFMonsterAIMgr* CRFMonsterAIMgr::Instance() {
    std::lock_guard<std::mutex> lock(ms_InstanceMutex);
    
    if (!ms_Instance) {
        ms_Instance = new CRFMonsterAIMgr();
    }
    
    return ms_Instance;
}

// Destroy the singleton instance
void CRFMonsterAIMgr::Destroy() {
    std::lock_guard<std::mutex> lock(ms_InstanceMutex);
    
    if (ms_Instance) {
        delete ms_Instance;
        ms_Instance = nullptr;
    }
}

// Initialize the AI manager
bool CRFMonsterAIMgr::Initialize() {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    try {
        InitializeInternal();
        m_isInitialized = true;
        
        // Initialize statistics
        m_statistics = {};
        m_statistics.lastUpdateTime = std::chrono::system_clock::now();
        
        return true;
    }
    catch (...) {
        m_isInitialized = false;
        return false;
    }
}

// Shutdown the AI manager
void CRFMonsterAIMgr::Shutdown() {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    m_isInitialized = false;
    ClearAIStates();
    ResetStateTables();
}

// Update AI processing
void CRFMonsterAIMgr::Update() {
    if (!m_isInitialized) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Process AI state updates here
    // This would contain the actual AI processing logic
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    // Update statistics
    m_statistics.totalProcessingTime += duration.count();
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
    UpdateStatistics();
}

// Get state table by index
UsPoint<UsStateTBL> CRFMonsterAIMgr::GetStateTBL(int32_t nIndex) const {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    // Validate index range (based on decompiled code: nIndex >= 1 || nIndex < 0 is invalid)
    if (nIndex < 0 || nIndex >= static_cast<int32_t>(GetMaxStateTableCount())) {
        return UsPoint<UsStateTBL>(); // Return empty UsPoint
    }
    
    return m_stateTables[nIndex];
}

// Set state table at specific index
bool CRFMonsterAIMgr::SetStateTBL(int32_t nIndex, const UsPoint<UsStateTBL>& stateTable) {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    if (nIndex < 0 || nIndex >= static_cast<int32_t>(GetMaxStateTableCount())) {
        return false;
    }
    
    m_stateTables[nIndex] = stateTable;
    return true;
}

// Add a new AI state entry
bool CRFMonsterAIMgr::AddAIState(const AIStateEntry& entry) {
    if (!m_isInitialized || !ValidateAIState(entry)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    // Check if state already exists
    if (FindAIState(entry.stateId) != nullptr) {
        return false; // State ID already exists
    }
    
    // Check capacity
    if (m_aiStates.size() >= GetMaxAIStateCount()) {
        return false; // At capacity
    }
    
    // Add the state
    m_aiStates.push_back(entry);
    return true;
}

// Remove an AI state by ID
bool CRFMonsterAIMgr::RemoveAIState(uint32_t stateId) {
    if (!m_isInitialized) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    auto it = std::find_if(m_aiStates.begin(), m_aiStates.end(),
        [stateId](const AIStateEntry& entry) {
            return entry.stateId == stateId;
        });
    
    if (it != m_aiStates.end()) {
        m_aiStates.erase(it);
        return true;
    }
    
    return false;
}

// Update an existing AI state
bool CRFMonsterAIMgr::UpdateAIState(uint32_t stateId, const AIStateEntry& entry) {
    if (!m_isInitialized || !ValidateAIState(entry)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    auto it = std::find_if(m_aiStates.begin(), m_aiStates.end(),
        [stateId](const AIStateEntry& existing) {
            return existing.stateId == stateId;
        });
    
    if (it != m_aiStates.end()) {
        *it = entry;
        return true;
    }
    
    return false;
}

// Find an AI state by ID
const AIStateEntry* CRFMonsterAIMgr::FindAIState(uint32_t stateId) const {
    auto it = std::find_if(m_aiStates.begin(), m_aiStates.end(),
        [stateId](const AIStateEntry& entry) {
            return entry.stateId == stateId;
        });
    
    return (it != m_aiStates.end()) ? &(*it) : nullptr;
}

// Get all AI states of a specific type
std::vector<const AIStateEntry*> CRFMonsterAIMgr::GetStatesByType(MonsterAIState state) const {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    std::vector<const AIStateEntry*> matchingStates;
    
    for (const auto& entry : m_aiStates) {
        if (entry.state == state) {
            matchingStates.push_back(&entry);
        }
    }
    
    return matchingStates;
}

// Get all active AI states
std::vector<const AIStateEntry*> CRFMonsterAIMgr::GetActiveStates() const {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    std::vector<const AIStateEntry*> activeStates;
    
    for (const auto& entry : m_aiStates) {
        if (entry.isActive) {
            activeStates.push_back(&entry);
        }
    }
    
    return activeStates;
}

// Check if the AI manager is initialized
bool CRFMonsterAIMgr::IsInitialized() const noexcept {
    return m_isInitialized;
}

// Get the number of state tables
std::size_t CRFMonsterAIMgr::GetStateTableCount() const noexcept {
    return GetMaxStateTableCount();
}

// Get the number of AI states
std::size_t CRFMonsterAIMgr::GetAIStateCount() const noexcept {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    return m_aiStates.size();
}

// Check if the AI manager is empty
bool CRFMonsterAIMgr::IsEmpty() const noexcept {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    return m_aiStates.empty();
}

// Get comprehensive statistics
AIManagerStatistics CRFMonsterAIMgr::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    UpdateStatistics();
    return m_statistics;
}

// Clear all AI states
void CRFMonsterAIMgr::ClearAIStates() {
    m_aiStates.clear();
    m_statistics = {};
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
}

// Reset all state tables
void CRFMonsterAIMgr::ResetStateTables() {
    for (auto& stateTable : m_stateTables) {
        stateTable = UsPoint<UsStateTBL>();
    }
}

// Validate an AI state entry
bool CRFMonsterAIMgr::ValidateAIState(const AIStateEntry& entry) {
    // Basic validation
    if (entry.stateId == 0) return false;
    if (entry.state > MonsterAIState::Custom) return false;
    if (entry.priority > AIPriority::Critical) return false;
    if (entry.duration > 3600000) return false; // Max 1 hour duration
    if (entry.cooldown > 3600000) return false; // Max 1 hour cooldown
    
    return true;
}

// Get memory usage of the AI manager
std::size_t CRFMonsterAIMgr::GetMemoryUsage() const {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    
    std::size_t size = sizeof(*this);
    
    // Add vector capacity
    size += m_aiStates.capacity() * sizeof(AIStateEntry);
    
    // Add string sizes
    for (const auto& entry : m_aiStates) {
        size += entry.stateName.capacity();
        size += entry.description.capacity();
    }
    
    return size;
}

// Initialize internal data structures
void CRFMonsterAIMgr::InitializeInternal() {
    // Reserve some initial capacity to avoid frequent reallocations
    m_aiStates.reserve(100);
    
    // Initialize all state tables to empty
    for (auto& stateTable : m_stateTables) {
        stateTable = UsPoint<UsStateTBL>();
    }
    
    // Initialize statistics
    m_statistics = {};
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
}

// Update statistics
void CRFMonsterAIMgr::UpdateStatistics() const {
    m_statistics.totalStates = m_aiStates.size();
    m_statistics.activeStates = 0;
    
    for (const auto& entry : m_aiStates) {
        if (entry.isActive) {
            m_statistics.activeStates++;
        }
    }
    
    // Calculate average processing time
    if (m_statistics.stateTransitions > 0) {
        m_statistics.averageProcessingTime = m_statistics.totalProcessingTime / m_statistics.stateTransitions;
    }
}

// Validate internal state
bool CRFMonsterAIMgr::ValidateInternalState() const {
    // Check if state count is within limits
    if (m_aiStates.size() > GetMaxAIStateCount()) {
        return false;
    }
    
    // Validate all AI states
    for (const auto& entry : m_aiStates) {
        if (!ValidateAIState(entry)) {
            return false;
        }
    }
    
    return true;
}

} // namespace World
} // namespace NexusProtection
